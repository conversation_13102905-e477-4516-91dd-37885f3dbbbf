import 'package:flutter/material.dart';
import '../models/blessing_text.dart';
import '../services/image_processor.dart';

/// 文字編輯畫面
/// 提供祝福語輸入、樣式調整等功能
class TextEditingScreen extends StatefulWidget {
  final String? initialText;
  final TextConfig? initialConfig;

  const TextEditingScreen({
    super.key,
    this.initialText,
    this.initialConfig,
  });

  @override
  State<TextEditingScreen> createState() => _TextEditingScreenState();
}

class _TextEditingScreenState extends State<TextEditingScreen> {
  late TextEditingController _textController;
  late TextConfig _textConfig;
  String _selectedCategory = '健康';

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.initialText ?? '');
    _textConfig = widget.initialConfig ??
        TextConfig(
          text: widget.initialText ?? '',
          fontSize: 48.0,
          textColor: Colors.white,
          fontWeight: FontWeight.bold,
          textAlign: TextAlign.center,
          position: TextPosition.center,
          hasBackground: true,
          backgroundColor: const Color(0x80000000),
          backgroundPadding: 16.0,
          backgroundRadius: 8.0,
          hasShadow: true,
        );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '編輯祝福語',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveAndReturn,
            child: const Text(
              '完成',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 文字預覽區
            _buildTextPreview(),
            const SizedBox(height: 24),
            
            // 文字輸入區
            _buildTextInput(),
            const SizedBox(height: 24),
            
            // 預設祝福語選擇
            _buildBlessingSelection(),
            const SizedBox(height: 24),
            
            // 文字樣式調整
            _buildTextStyleControls(),
          ],
        ),
      ),
    );
  }

  /// 建立文字預覽區
  Widget _buildTextPreview() {
    return Card(
      elevation: 4,
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            colors: [Colors.blue, Colors.purple],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Container(
            padding: EdgeInsets.all(_textConfig.backgroundPadding),
            decoration: _textConfig.hasBackground
                ? BoxDecoration(
                    color: _textConfig.backgroundColor,
                    borderRadius: BorderRadius.circular(_textConfig.backgroundRadius),
                  )
                : null,
            child: Text(
              _textController.text.isEmpty ? '預覽文字' : _textController.text,
              style: TextStyle(
                fontSize: _textConfig.fontSize * 0.5, // 縮小顯示
                color: _textConfig.textColor,
                fontWeight: _textConfig.fontWeight,
                fontFamily: _textConfig.fontFamily,
                shadows: _textConfig.hasShadow
                    ? [
                        Shadow(
                          offset: const Offset(1, 1),
                          blurRadius: 2,
                          color: Colors.black.withValues(alpha: 0.5),
                        ),
                      ]
                    : null,
              ),
              textAlign: _textConfig.textAlign,
            ),
          ),
        ),
      ),
    );
  }

  /// 建立文字輸入區
  Widget _buildTextInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '輸入祝福語',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _textController,
              maxLines: 3,
              style: const TextStyle(fontSize: 18),
              decoration: const InputDecoration(
                hintText: '請輸入您的祝福語...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.all(16),
              ),
              onChanged: (text) {
                setState(() {
                  _textConfig = TextConfig(
                    text: text,
                    fontSize: _textConfig.fontSize,
                    textColor: _textConfig.textColor,
                    fontWeight: _textConfig.fontWeight,
                    fontFamily: _textConfig.fontFamily,
                    textAlign: _textConfig.textAlign,
                    position: _textConfig.position,
                    customOffset: _textConfig.customOffset,
                    hasBackground: _textConfig.hasBackground,
                    backgroundColor: _textConfig.backgroundColor,
                    backgroundPadding: _textConfig.backgroundPadding,
                    backgroundRadius: _textConfig.backgroundRadius,
                    hasShadow: _textConfig.hasShadow,
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 建立預設祝福語選擇區
  Widget _buildBlessingSelection() {
    final categories = BlessingText.getAllCategories();
    final blessings = BlessingText.getBlessingsByCategory(_selectedCategory);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '選擇預設祝福語',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // 分類選擇
            SizedBox(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(
                        category,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black,
                          fontSize: 16,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() => _selectedCategory = category);
                        }
                      },
                      backgroundColor: Colors.grey.shade200,
                      selectedColor: Theme.of(context).primaryColor,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            
            // 祝福語列表
            SizedBox(
              height: 120,
              child: ListView.builder(
                itemCount: blessings.length,
                itemBuilder: (context, index) {
                  final blessing = blessings[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(
                        blessing.text.replaceAll('\n', ' '),
                        style: const TextStyle(fontSize: 16),
                      ),
                      onTap: () {
                        _textController.text = blessing.text;
                        setState(() {
                          _textConfig = TextConfig(
                            text: blessing.text,
                            fontSize: _textConfig.fontSize,
                            textColor: _textConfig.textColor,
                            fontWeight: _textConfig.fontWeight,
                            fontFamily: _textConfig.fontFamily,
                            textAlign: _textConfig.textAlign,
                            position: _textConfig.position,
                            customOffset: _textConfig.customOffset,
                            hasBackground: _textConfig.hasBackground,
                            backgroundColor: _textConfig.backgroundColor,
                            backgroundPadding: _textConfig.backgroundPadding,
                            backgroundRadius: _textConfig.backgroundRadius,
                            hasShadow: _textConfig.hasShadow,
                          );
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 建立文字樣式控制區
  Widget _buildTextStyleControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '文字樣式',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // 字體大小
            _buildSliderControl(
              label: '字體大小',
              value: _textConfig.fontSize,
              min: 24.0,
              max: 72.0,
              divisions: 24,
              onChanged: (value) {
                setState(() {
                  _textConfig = _textConfig.copyWith(fontSize: value);
                });
              },
            ),
            
            // 文字顏色
            _buildColorPicker(),
            
            // 文字對齊
            _buildAlignmentPicker(),
            
            // 背景設定
            _buildBackgroundControls(),
          ],
        ),
      ),
    );
  }

  /// 建立滑桿控制項
  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.round()}',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  /// 建立顏色選擇器
  Widget _buildColorPicker() {
    final colors = [
      Colors.white,
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.yellow,
      Colors.orange,
      Colors.purple,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '文字顏色',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: colors.map((color) {
            final isSelected = _textConfig.textColor == color;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _textConfig = _textConfig.copyWith(textColor: color);
                });
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: isSelected ? 3 : 1,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  /// 建立對齊方式選擇器
  Widget _buildAlignmentPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '文字對齊',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildAlignmentButton(TextAlign.left, Icons.format_align_left),
            _buildAlignmentButton(TextAlign.center, Icons.format_align_center),
            _buildAlignmentButton(TextAlign.right, Icons.format_align_right),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  /// 建立對齊按鈕
  Widget _buildAlignmentButton(TextAlign align, IconData icon) {
    final isSelected = _textConfig.textAlign == align;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: IconButton(
        onPressed: () {
          setState(() {
            _textConfig = _textConfig.copyWith(textAlign: align);
          });
        },
        icon: Icon(icon),
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
        iconSize: 32,
      ),
    );
  }

  /// 建立背景控制項
  Widget _buildBackgroundControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '文字背景',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            Switch(
              value: _textConfig.hasBackground,
              onChanged: (value) {
                setState(() {
                  _textConfig = _textConfig.copyWith(hasBackground: value);
                });
              },
            ),
          ],
        ),
        if (_textConfig.hasBackground) ...[
          const SizedBox(height: 8),
          _buildSliderControl(
            label: '背景透明度',
            value: _textConfig.backgroundColor.alpha / 255.0,
            min: 0.0,
            max: 1.0,
            divisions: 10,
            onChanged: (value) {
              setState(() {
                _textConfig = _textConfig.copyWith(
                  backgroundColor: _textConfig.backgroundColor.withValues(
                    alpha: value,
                  ),
                );
              });
            },
          ),
        ],
      ],
    );
  }

  /// 儲存並返回
  void _saveAndReturn() {
    final finalConfig = TextConfig(
      text: _textController.text,
      fontSize: _textConfig.fontSize,
      textColor: _textConfig.textColor,
      fontWeight: _textConfig.fontWeight,
      fontFamily: _textConfig.fontFamily,
      textAlign: _textConfig.textAlign,
      position: _textConfig.position,
      customOffset: _textConfig.customOffset,
      hasBackground: _textConfig.hasBackground,
      backgroundColor: _textConfig.backgroundColor,
      backgroundPadding: _textConfig.backgroundPadding,
      backgroundRadius: _textConfig.backgroundRadius,
      hasShadow: _textConfig.hasShadow,
    );
    
    Navigator.of(context).pop(finalConfig);
  }
}

/// TextConfig 擴展方法
extension TextConfigExtension on TextConfig {
  TextConfig copyWith({
    String? text,
    double? fontSize,
    Color? textColor,
    FontWeight? fontWeight,
    String? fontFamily,
    TextAlign? textAlign,
    TextPosition? position,
    Offset? customOffset,
    bool? hasBackground,
    Color? backgroundColor,
    double? backgroundPadding,
    double? backgroundRadius,
    bool? hasShadow,
  }) {
    return TextConfig(
      text: text ?? this.text,
      fontSize: fontSize ?? this.fontSize,
      textColor: textColor ?? this.textColor,
      fontWeight: fontWeight ?? this.fontWeight,
      fontFamily: fontFamily ?? this.fontFamily,
      textAlign: textAlign ?? this.textAlign,
      position: position ?? this.position,
      customOffset: customOffset ?? this.customOffset,
      hasBackground: hasBackground ?? this.hasBackground,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      backgroundPadding: backgroundPadding ?? this.backgroundPadding,
      backgroundRadius: backgroundRadius ?? this.backgroundRadius,
      hasShadow: hasShadow ?? this.hasShadow,
    );
  }
}
