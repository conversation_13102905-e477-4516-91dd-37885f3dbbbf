# 長輩圖產生器 (Everyday Safety)

一個專為年長者設計的 Flutter 應用程式，提供簡潔直覺的介面來製作長輩圖。

## 🎯 功能特色

### 📱 核心功能
- **圖片來源選擇**：支援內建圖庫、手機相簿、相機拍攝
- **祝福語編輯**：提供預設祝福語庫，支援自訂文字
- **文字樣式調整**：字體大小、顏色、對齊方式、背景設定
- **圖片編輯合成**：使用 CustomPaint 進行文字疊加
- **儲存與分享**：完整的跨平台圖片儲存和分享功能

### 🎨 設計理念
- **大按鈕大字體**：適合長輩使用的友善介面
- **簡潔操作流程**：三步驟完成長輩圖製作
- **離線優先**：所有功能都可離線使用
- **權限友善**：完整的權限處理和說明

## 🏗️ 專案結構

```
lib/
├── main.dart                 # 應用程式入口
├── models/                   # 資料模型
│   ├── blessing_text.dart    # 祝福語模型
│   └── image_source.dart     # 圖片來源模型
├── screens/                  # 畫面
│   ├── home_screen.dart      # 主畫面
│   ├── image_selection_screen.dart  # 圖片選擇畫面
│   ├── text_editing_screen.dart     # 文字編輯畫面
│   └── image_editing_screen.dart    # 圖片編輯畫面
├── services/                 # 服務層
│   ├── permission_service.dart      # 權限管理
│   ├── image_picker_service.dart    # 圖片選擇服務
│   ├── image_processor.dart         # 圖片處理服務
│   └── image_saver.dart            # 圖片儲存服務
└── widgets/                  # 自訂元件
```

## 📦 依賴套件

- `image_picker`: 圖片選擇
- `image_gallery_saver`: 圖片儲存到相簿
- `permission_handler`: 權限管理
- `share_plus`: 分享功能
- `google_fonts`: 字型支援
- `image`: 圖片處理
- `path_provider`: 路徑管理

## 🚀 安裝與執行

### 前置需求
- Flutter SDK 3.7.x
- Dart 2.x
- Android Studio / VS Code

### 安裝步驟

1. **安裝依賴**
   ```bash
   flutter pub get
   ```

2. **準備 Assets 資源**
   - 下載 Noto Sans TC 字型檔案到 `assets/fonts/`
   - 準備示例圖片到 `assets/images/`

3. **執行應用程式**
   ```bash
   flutter run
   ```

## 📱 平台支援

### Android
- 支援 Android 9+ (API 28+)
- 自動處理不同版本的儲存權限
- 完整的相機和相簿權限管理

### iOS
- 支援 iOS 12+
- 完整的照片權限處理
- 原生分享功能整合

## 🔧 開發指南

### 程式碼規範
- 使用 Dart 2.x 語法，禁用 `new` 關鍵字
- 遵循 `flutter format` 格式化規則
- 所有函式和類別需要 DartDoc 註解
- 變數使用 camelCase，類別使用 PascalCase

### 測試
```bash
# 程式碼分析
flutter analyze

# 執行測試（需要先準備字型檔案）
flutter test
```

## 📋 待完成功能

- [ ] AI 圖片產生功能
- [ ] 更多內建圖片和字型
- [ ] 圖片濾鏡效果
- [ ] 批量處理功能

## 🐛 已知問題與解決方案

### 1. 字型檔案缺失
**問題**：執行時提示找不到字型檔案
**解決方案**：
1. 前往 [Google Fonts](https://fonts.google.com/noto/specimen/Noto+Sans+TC)
2. 下載 Noto Sans TC 字型
3. 將 `NotoSansTC-Regular.ttf` 和 `NotoSansTC-Bold.ttf` 放入 `assets/fonts/`

### 2. Android 編譯問題
**問題**：某些套件 namespace 錯誤
**解決方案**：
```bash
flutter clean
flutter pub get
flutter pub upgrade
```

### 3. 圖片資源缺失
**問題**：內建圖庫顯示空白
**解決方案**：準備示例圖片檔案放入 `assets/images/` 資料夾

## 📄 授權

本專案使用 MIT 授權條款。
