import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

/// 圖片處理服務
/// 負責圖片與文字的合成處理
class ImageProcessor {
  /// 將文字疊加到圖片上
  ///
  /// [backgroundImage] 背景圖片
  /// [textConfig] 文字配置
  ///
  /// 回傳合成後的圖片位元組資料
  static Future<Uint8List> addTextToImage(
    ui.Image backgroundImage,
    TextConfig textConfig,
  ) async {
    // 建立畫布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // 取得圖片尺寸
    final imageWidth = backgroundImage.width.toDouble();
    final imageHeight = backgroundImage.height.toDouble();

    // 繪製背景圖片
    canvas.drawImage(backgroundImage, Offset.zero, Paint());

    // 準備文字樣式
    final textStyle = TextStyle(
      fontSize: textConfig.fontSize,
      color: textConfig.textColor,
      fontWeight: textConfig.fontWeight,
      fontFamily: textConfig.fontFamily,
      shadows:
          textConfig.hasShadow
              ? [
                Shadow(
                  offset: const Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black.withValues(alpha: 0.5),
                ),
              ]
              : null,
    );

    // 建立文字段落
    final textSpan = TextSpan(text: textConfig.text, style: textStyle);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: textConfig.textAlign,
    );

    // 計算文字佈局
    textPainter.layout(maxWidth: imageWidth * 0.8); // 限制文字寬度

    // 計算文字位置
    final textOffset = _calculateTextPosition(
      imageWidth,
      imageHeight,
      textPainter.size,
      textConfig.position,
      textConfig.customOffset,
    );

    // 繪製文字背景（如果有設定）
    if (textConfig.hasBackground) {
      final backgroundRect = Rect.fromLTWH(
        textOffset.dx - textConfig.backgroundPadding,
        textOffset.dy - textConfig.backgroundPadding,
        textPainter.size.width + textConfig.backgroundPadding * 2,
        textPainter.size.height + textConfig.backgroundPadding * 2,
      );

      final backgroundPaint =
          Paint()
            ..color = textConfig.backgroundColor
            ..style = PaintingStyle.fill;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          backgroundRect,
          Radius.circular(textConfig.backgroundRadius),
        ),
        backgroundPaint,
      );
    }

    // 繪製文字
    textPainter.paint(canvas, textOffset);

    // 完成繪製
    final picture = recorder.endRecording();
    final finalImage = await picture.toImage(
      backgroundImage.width,
      backgroundImage.height,
    );

    // 轉換為位元組資料
    final byteData = await finalImage.toByteData(
      format: ui.ImageByteFormat.png,
    );
    return byteData!.buffer.asUint8List();
  }

  /// 計算文字位置
  static Offset _calculateTextPosition(
    double imageWidth,
    double imageHeight,
    Size textSize,
    TextPosition position,
    Offset? customOffset,
  ) {
    if (customOffset != null) {
      return customOffset;
    }

    switch (position) {
      case TextPosition.topLeft:
        return Offset(imageWidth * 0.05, imageHeight * 0.05);
      case TextPosition.topCenter:
        return Offset((imageWidth - textSize.width) / 2, imageHeight * 0.05);
      case TextPosition.topRight:
        return Offset(imageWidth * 0.95 - textSize.width, imageHeight * 0.05);
      case TextPosition.centerLeft:
        return Offset(imageWidth * 0.05, (imageHeight - textSize.height) / 2);
      case TextPosition.center:
        return Offset(
          (imageWidth - textSize.width) / 2,
          (imageHeight - textSize.height) / 2,
        );
      case TextPosition.centerRight:
        return Offset(
          imageWidth * 0.95 - textSize.width,
          (imageHeight - textSize.height) / 2,
        );
      case TextPosition.bottomLeft:
        return Offset(imageWidth * 0.05, imageHeight * 0.95 - textSize.height);
      case TextPosition.bottomCenter:
        return Offset(
          (imageWidth - textSize.width) / 2,
          imageHeight * 0.95 - textSize.height,
        );
      case TextPosition.bottomRight:
        return Offset(
          imageWidth * 0.95 - textSize.width,
          imageHeight * 0.95 - textSize.height,
        );
    }
  }

  /// 從檔案載入圖片
  static Future<ui.Image> loadImageFromFile(String filePath) async {
    final bytes = await File(filePath).readAsBytes();
    return await loadImageFromBytes(bytes);
  }

  /// 從位元組資料載入圖片
  static Future<ui.Image> loadImageFromBytes(Uint8List bytes) async {
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// 調整圖片大小
  static Future<Uint8List> resizeImage(
    Uint8List imageBytes,
    int maxWidth,
    int maxHeight,
  ) async {
    final image = img.decodeImage(imageBytes);
    if (image == null) throw Exception('無法解碼圖片');

    final resized = img.copyResize(
      image,
      width: maxWidth,
      height: maxHeight,
      interpolation: img.Interpolation.linear,
    );

    return Uint8List.fromList(img.encodeJpg(resized, quality: 90));
  }
}

/// 文字配置類別
class TextConfig {
  final String text;
  final double fontSize;
  final Color textColor;
  final FontWeight fontWeight;
  final String? fontFamily;
  final TextAlign textAlign;
  final TextPosition position;
  final Offset? customOffset;
  final bool hasBackground;
  final Color backgroundColor;
  final double backgroundPadding;
  final double backgroundRadius;
  final bool hasShadow;

  TextConfig({
    required this.text,
    this.fontSize = 48.0,
    this.textColor = Colors.white,
    this.fontWeight = FontWeight.bold,
    this.fontFamily,
    this.textAlign = TextAlign.center,
    this.position = TextPosition.center,
    this.customOffset,
    this.hasBackground = true,
    this.backgroundColor = const Color(0x80000000),
    this.backgroundPadding = 16.0,
    this.backgroundRadius = 8.0,
    this.hasShadow = true,
  });
}

/// 文字位置枚舉
enum TextPosition {
  topLeft,
  topCenter,
  topRight,
  centerLeft,
  center,
  centerRight,
  bottomLeft,
  bottomCenter,
  bottomRight,
}
