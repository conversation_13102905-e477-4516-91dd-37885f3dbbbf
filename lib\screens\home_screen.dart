import 'package:flutter/material.dart';
import '../models/image_source.dart';
import 'image_selection_screen.dart';
import 'image_editing_screen.dart';

/// 主畫面
/// 提供簡潔易用的長輩圖製作入口
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          '長輩圖產生器',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // 歡迎區域
              _buildWelcomeSection(),
              
              const SizedBox(height: 40),
              
              // 主要功能按鈕
              Expanded(child: _buildMainActions()),
              
              // 底部提示
              _buildBottomTips(),
            ],
          ),
        ),
      ),
    );
  }

  /// 建立歡迎區域
  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.purple.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          Icon(
            Icons.auto_awesome,
            size: 64,
            color: Colors.white,
          ),
          SizedBox(height: 16),
          Text(
            '歡迎使用長輩圖產生器',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            '簡單三步驟，製作專屬祝福圖片',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 建立主要操作區域
  Widget _buildMainActions() {
    return Column(
      children: [
        // 步驟說明
        _buildStepsGuide(),
        
        const SizedBox(height: 32),
        
        // 開始製作按鈕
        _buildStartButton(),
        
        const SizedBox(height: 24),
        
        // 快速選項
        _buildQuickOptions(),
      ],
    );
  }

  /// 建立步驟指引
  Widget _buildStepsGuide() {
    return Row(
      children: [
        Expanded(child: _buildStepCard('1', '選擇圖片', Icons.image)),
        const SizedBox(width: 16),
        Expanded(child: _buildStepCard('2', '添加文字', Icons.text_fields)),
        const SizedBox(width: 16),
        Expanded(child: _buildStepCard('3', '儲存分享', Icons.share)),
      ],
    );
  }

  /// 建立步驟卡片
  Widget _buildStepCard(String step, String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Icon(icon, size: 32, color: Colors.grey.shade600),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 建立開始製作按鈕
  Widget _buildStartButton() {
    return SizedBox(
      width: double.infinity,
      height: 80,
      child: ElevatedButton(
        onPressed: _startCreating,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade500,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          shadowColor: Colors.green.shade200,
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 36),
            SizedBox(width: 16),
            Text(
              '開始製作長輩圖',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 建立快速選項
  Widget _buildQuickOptions() {
    return Column(
      children: [
        const Text(
          '或選擇快速開始',
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickOptionButton(
                title: '內建圖庫',
                icon: Icons.photo_library,
                color: Colors.blue,
                onTap: () => _quickStart(ImageSourceType.builtin),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickOptionButton(
                title: '手機相簿',
                icon: Icons.photo,
                color: Colors.green,
                onTap: () => _quickStart(ImageSourceType.gallery),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickOptionButton(
                title: '相機拍攝',
                icon: Icons.camera_alt,
                color: Colors.orange,
                onTap: () => _quickStart(ImageSourceType.camera),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 建立快速選項按鈕
  Widget _buildQuickOptionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 建立底部提示
  Widget _buildBottomTips() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.lightbulb, color: Colors.amber.shade700, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '提示：所有功能都可離線使用，您的圖片不會上傳到網路',
              style: TextStyle(
                fontSize: 14,
                color: Colors.amber.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 開始製作
  Future<void> _startCreating() async {
    final imageSource = await Navigator.of(context).push<ImageSource>(
      MaterialPageRoute(
        builder: (context) => const ImageSelectionScreen(),
      ),
    );

    if (imageSource != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageEditingScreen(imageSource: imageSource),
        ),
      );
    }
  }

  /// 快速開始
  Future<void> _quickStart(ImageSourceType type) async {
    // 這裡可以直接根據類型進行相應的操作
    // 暫時先導向圖片選擇畫面
    await _startCreating();
  }
}
