import 'dart:io';
import 'dart:typed_data';
import 'package:gal/gal.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'permission_service.dart';

/// 圖片儲存與分享服務
/// 負責將產生的長輩圖儲存到相簿並提供分享功能
class ImageSaver {
  /// 儲存圖片到相簿
  ///
  /// [imageBytes] 圖片的位元組資料
  /// [fileName] 檔案名稱（可選）
  ///
  /// 回傳 [ImageSaveResult] 包含儲存結果和訊息
  static Future<ImageSaveResult> saveToGallery(
    Uint8List imageBytes, {
    String? fileName,
  }) async {
    try {
      // 檢查權限
      final hasPermission = await PermissionService.checkPhotoPermission();
      if (!hasPermission) {
        final granted = await PermissionService.requestPhotoPermission();
        if (!granted) {
          return ImageSaveResult(
            success: false,
            message: '需要相簿權限才能儲存圖片',
            needsPermission: true,
          );
        }
      }

      // 產生檔案名稱
      final name = fileName ?? _generateFileName();

      // 建立暫存檔案
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$name');
      await file.writeAsBytes(imageBytes);

      // 使用 gal 儲存到相簿
      await Gal.putImage(file.path, album: 'EverydaySafety');

      return ImageSaveResult(
        success: true,
        message: '圖片已成功儲存到相簿',
        filePath: file.path,
      );
    } catch (e) {
      return ImageSaveResult(success: false, message: '儲存時發生錯誤：$e');
    }
  }

  /// 分享圖片
  ///
  /// [imageBytes] 圖片的位元組資料
  /// [fileName] 檔案名稱（可選）
  /// [text] 分享時的文字內容（可選）
  static Future<ShareResult> shareImage(
    Uint8List imageBytes, {
    String? fileName,
    String? text,
  }) async {
    try {
      // 取得暫存目錄
      final tempDir = await getTemporaryDirectory();
      final name = fileName ?? _generateFileName();
      final file = File('${tempDir.path}/$name');

      // 寫入暫存檔案
      await file.writeAsBytes(imageBytes);

      // 分享檔案
      await Share.shareXFiles([
        XFile(file.path),
      ], text: text ?? '我用長輩圖產生器製作了這張圖片！');

      return ShareResult(success: true, message: '分享成功');
    } catch (e) {
      return ShareResult(success: false, message: '分享時發生錯誤：$e');
    }
  }

  /// 同時儲存並分享圖片
  static Future<CombinedResult> saveAndShare(
    Uint8List imageBytes, {
    String? fileName,
    String? shareText,
  }) async {
    final saveResult = await saveToGallery(imageBytes, fileName: fileName);
    final shareResult = await shareImage(
      imageBytes,
      fileName: fileName,
      text: shareText,
    );

    return CombinedResult(saveResult: saveResult, shareResult: shareResult);
  }

  /// 產生檔案名稱
  static String _generateFileName() {
    final now = DateTime.now();
    return '長輩圖_${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}.jpg';
  }
}

/// 圖片儲存結果
class ImageSaveResult {
  final bool success;
  final String message;
  final String? filePath;
  final bool needsPermission;

  ImageSaveResult({
    required this.success,
    required this.message,
    this.filePath,
    this.needsPermission = false,
  });
}

/// 分享結果
class ShareResult {
  final bool success;
  final String message;

  ShareResult({required this.success, required this.message});
}

/// 組合結果（儲存+分享）
class CombinedResult {
  final ImageSaveResult saveResult;
  final ShareResult shareResult;

  CombinedResult({required this.saveResult, required this.shareResult});

  bool get allSuccess => saveResult.success && shareResult.success;

  String get combinedMessage {
    if (allSuccess) {
      return '圖片已儲存並準備分享';
    } else if (saveResult.success) {
      return '圖片已儲存，但分享失敗：${shareResult.message}';
    } else if (shareResult.success) {
      return '分享成功，但儲存失敗：${saveResult.message}';
    } else {
      return '儲存和分享都失敗';
    }
  }
}
