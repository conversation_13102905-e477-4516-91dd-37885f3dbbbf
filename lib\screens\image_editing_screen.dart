import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../models/image_source.dart';
import '../services/image_processor.dart';
import '../services/image_saver.dart';
import 'text_editing_screen.dart';

/// 圖片編輯畫面
/// 提供圖片與文字的合成編輯功能
class ImageEditingScreen extends StatefulWidget {
  final ImageSource imageSource;

  const ImageEditingScreen({super.key, required this.imageSource});

  @override
  State<ImageEditingScreen> createState() => _ImageEditingScreenState();
}

class _ImageEditingScreenState extends State<ImageEditingScreen> {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  ui.Image? _backgroundImage;
  TextConfig? _textConfig;
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadBackgroundImage();
  }

  /// 載入背景圖片
  Future<void> _loadBackgroundImage() async {
    try {
      setState(() => _isLoading = true);

      if (widget.imageSource.isLocal && widget.imageSource.path != null) {
        _backgroundImage = await ImageProcessor.loadImageFromFile(
          widget.imageSource.path!,
        );
      } else {
        // 處理網路圖片或其他來源
        throw Exception('暫不支援此圖片來源');
      }

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        _showErrorDialog('載入圖片失敗：$e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '編輯長輩圖',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_textConfig != null)
            IconButton(
              onPressed: _saveImage,
              icon: const Icon(Icons.save),
              tooltip: '儲存圖片',
            ),
          if (_textConfig != null)
            IconButton(
              onPressed: _shareImage,
              icon: const Icon(Icons.share),
              tooltip: '分享圖片',
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _isProcessing
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在處理圖片...', style: TextStyle(fontSize: 18)),
                  ],
                ),
              )
              : Column(
                children: [
                  // 圖片預覽區
                  Expanded(child: _buildImagePreview()),

                  // 控制按鈕區
                  _buildControlButtons(),
                ],
              ),
    );
  }

  /// 建立圖片預覽區
  Widget _buildImagePreview() {
    if (_backgroundImage == null) {
      return const Center(
        child: Text('無法載入圖片', style: TextStyle(fontSize: 18)),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: RepaintBoundary(
        key: _repaintBoundaryKey,
        child: AspectRatio(
          aspectRatio: _backgroundImage!.width / _backgroundImage!.height,
          child: CustomPaint(
            painter: ImageWithTextPainter(
              backgroundImage: _backgroundImage!,
              textConfig: _textConfig,
            ),
            child: GestureDetector(
              onTapDown: _textConfig != null ? _onTapDown : null,
              child: Container(),
            ),
          ),
        ),
      ),
    );
  }

  /// 建立控制按鈕區
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          if (_textConfig == null) ...[
            // 添加文字按鈕
            SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton.icon(
                onPressed: _addText,
                icon: const Icon(Icons.text_fields, size: 28),
                label: const Text(
                  '添加祝福語',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ] else ...[
            // 文字編輯控制按鈕
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _editText,
                    icon: const Icon(Icons.edit),
                    label: const Text('編輯文字'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _removeText,
                    icon: const Icon(Icons.delete),
                    label: const Text('移除文字'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _saveImage,
                    icon: const Icon(Icons.save),
                    label: const Text('儲存圖片'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _shareImage,
                    icon: const Icon(Icons.share),
                    label: const Text('分享圖片'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 8),
          const Text(
            '💡 提示：點擊圖片可調整文字位置',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 處理點擊事件（調整文字位置）
  void _onTapDown(TapDownDetails details) {
    if (_textConfig == null || _backgroundImage == null) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);

    // 計算相對於圖片的位置
    final imageWidth = _backgroundImage!.width.toDouble();
    final imageHeight = _backgroundImage!.height.toDouble();

    // 這裡需要根據實際的圖片顯示區域計算正確的位置
    // 簡化處理，直接使用點擊位置
    setState(() {
      _textConfig = _textConfig!.copyWith(
        customOffset: Offset(
          localPosition.dx.clamp(0, imageWidth),
          localPosition.dy.clamp(0, imageHeight),
        ),
      );
    });
  }

  /// 添加文字
  Future<void> _addText() async {
    final textConfig = await Navigator.of(context).push<TextConfig>(
      MaterialPageRoute(
        builder:
            (context) => const TextEditingScreen(initialText: '身體健康\n萬事如意'),
      ),
    );

    if (textConfig != null) {
      setState(() {
        _textConfig = textConfig;
      });
    }
  }

  /// 編輯文字
  Future<void> _editText() async {
    if (_textConfig == null) return;

    final textConfig = await Navigator.of(context).push<TextConfig>(
      MaterialPageRoute(
        builder:
            (context) => TextEditingScreen(
              initialText: _textConfig!.text,
              initialConfig: _textConfig,
            ),
      ),
    );

    if (textConfig != null) {
      setState(() {
        _textConfig = textConfig;
      });
    }
  }

  /// 移除文字
  void _removeText() {
    setState(() {
      _textConfig = null;
    });
  }

  /// 儲存圖片
  Future<void> _saveImage() async {
    if (_backgroundImage == null || _textConfig == null) return;

    setState(() => _isProcessing = true);

    try {
      // 擷取合成後的圖片
      final boundary =
          _repaintBoundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;
      final image = await boundary.toImage(pixelRatio: 2.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final imageBytes = byteData!.buffer.asUint8List();

      // 儲存到相簿
      final result = await ImageSaver.saveToGallery(imageBytes);

      if (mounted) {
        if (result.success) {
          _showSuccessDialog('圖片已成功儲存到相簿！');
        } else {
          _showErrorDialog(result.message);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('儲存圖片時發生錯誤：$e');
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  /// 分享圖片
  Future<void> _shareImage() async {
    if (_backgroundImage == null || _textConfig == null) return;

    setState(() => _isProcessing = true);

    try {
      // 擷取合成後的圖片
      final boundary =
          _repaintBoundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;
      final image = await boundary.toImage(pixelRatio: 2.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final imageBytes = byteData!.buffer.asUint8List();

      // 分享圖片
      final result = await ImageSaver.shareImage(
        imageBytes,
        text: '我用長輩圖產生器製作了這張圖片！',
      );

      if (mounted && !result.success) {
        _showErrorDialog(result.message);
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('分享圖片時發生錯誤：$e');
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  /// 顯示成功對話框
  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('成功'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  /// 顯示錯誤對話框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('錯誤'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }
}

/// 自訂畫筆：繪製背景圖片和文字
class ImageWithTextPainter extends CustomPainter {
  final ui.Image backgroundImage;
  final TextConfig? textConfig;

  ImageWithTextPainter({required this.backgroundImage, this.textConfig});

  @override
  void paint(Canvas canvas, Size size) {
    // 繪製背景圖片
    final paint = Paint();
    canvas.drawImageRect(
      backgroundImage,
      Rect.fromLTWH(
        0,
        0,
        backgroundImage.width.toDouble(),
        backgroundImage.height.toDouble(),
      ),
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );

    // 繪製文字
    if (textConfig != null && textConfig!.text.isNotEmpty) {
      _drawText(canvas, size);
    }
  }

  void _drawText(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      fontSize: textConfig!.fontSize * (size.width / backgroundImage.width),
      color: textConfig!.textColor,
      fontWeight: textConfig!.fontWeight,
      fontFamily: textConfig!.fontFamily,
      shadows:
          textConfig!.hasShadow
              ? [
                Shadow(
                  offset: const Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black.withValues(alpha: 0.5),
                ),
              ]
              : null,
    );

    final textSpan = TextSpan(text: textConfig!.text, style: textStyle);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: textConfig!.textAlign,
    );

    textPainter.layout(maxWidth: size.width * 0.8);

    // 計算文字位置
    Offset textOffset;
    if (textConfig!.customOffset != null) {
      textOffset = Offset(
        textConfig!.customOffset!.dx * (size.width / backgroundImage.width),
        textConfig!.customOffset!.dy * (size.height / backgroundImage.height),
      );
    } else {
      textOffset = _calculateTextPosition(size, textPainter.size);
    }

    // 繪製文字背景
    if (textConfig!.hasBackground) {
      final backgroundRect = Rect.fromLTWH(
        textOffset.dx - textConfig!.backgroundPadding,
        textOffset.dy - textConfig!.backgroundPadding,
        textPainter.size.width + textConfig!.backgroundPadding * 2,
        textPainter.size.height + textConfig!.backgroundPadding * 2,
      );

      final backgroundPaint =
          Paint()
            ..color = textConfig!.backgroundColor
            ..style = PaintingStyle.fill;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          backgroundRect,
          Radius.circular(textConfig!.backgroundRadius),
        ),
        backgroundPaint,
      );
    }

    // 繪製文字
    textPainter.paint(canvas, textOffset);
  }

  Offset _calculateTextPosition(Size canvasSize, Size textSize) {
    switch (textConfig!.position) {
      case TextPosition.topLeft:
        return Offset(canvasSize.width * 0.05, canvasSize.height * 0.05);
      case TextPosition.topCenter:
        return Offset(
          (canvasSize.width - textSize.width) / 2,
          canvasSize.height * 0.05,
        );
      case TextPosition.topRight:
        return Offset(
          canvasSize.width * 0.95 - textSize.width,
          canvasSize.height * 0.05,
        );
      case TextPosition.centerLeft:
        return Offset(
          canvasSize.width * 0.05,
          (canvasSize.height - textSize.height) / 2,
        );
      case TextPosition.center:
        return Offset(
          (canvasSize.width - textSize.width) / 2,
          (canvasSize.height - textSize.height) / 2,
        );
      case TextPosition.centerRight:
        return Offset(
          canvasSize.width * 0.95 - textSize.width,
          (canvasSize.height - textSize.height) / 2,
        );
      case TextPosition.bottomLeft:
        return Offset(
          canvasSize.width * 0.05,
          canvasSize.height * 0.95 - textSize.height,
        );
      case TextPosition.bottomCenter:
        return Offset(
          (canvasSize.width - textSize.width) / 2,
          canvasSize.height * 0.95 - textSize.height,
        );
      case TextPosition.bottomRight:
        return Offset(
          canvasSize.width * 0.95 - textSize.width,
          canvasSize.height * 0.95 - textSize.height,
        );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
