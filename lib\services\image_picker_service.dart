import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart' as picker;
import '../models/image_source.dart' as model;
import 'permission_service.dart';

/// 圖片選擇服務
/// 處理各種圖片來源的選擇邏輯
class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  /// 從相簿選擇圖片
  static Future<ImageSource?> pickFromGallery() async {
    try {
      // 檢查權限
      final hasPermission = await PermissionService.checkPhotoPermission();
      if (!hasPermission) {
        final granted = await PermissionService.requestPhotoPermission();
        if (!granted) {
          throw Exception('需要相簿權限才能選擇圖片');
        }
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        return ImageSource.fromGallery(image.path);
      }
      return null;
    } catch (e) {
      throw Exception('選擇圖片時發生錯誤：$e');
    }
  }

  /// 使用相機拍攝圖片
  static Future<ImageSource?> pickFromCamera() async {
    try {
      // 檢查相機權限
      final hasCameraPermission =
          await PermissionService.requestCameraPermission();
      if (!hasCameraPermission) {
        throw Exception('需要相機權限才能拍攝圖片');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        return ImageSource.fromCamera(image.path);
      }
      return null;
    } catch (e) {
      throw Exception('拍攝圖片時發生錯誤：$e');
    }
  }

  /// 取得內建圖片列表
  static List<ImageSource> getBuiltinImages() {
    return ImageSource.getBuiltinImages();
  }

  /// 顯示圖片選擇對話框
  static Future<ImageSource?> showImageSourceDialog(
    BuildContext context,
  ) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '選擇圖片來源',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSourceOption(
                context,
                title: '內建圖庫',
                description: '從預設圖片中選擇',
                icon: Icons.photo_library,
                onTap: () {
                  Navigator.of(context).pop();
                  _showBuiltinImagesDialog(context);
                },
              ),
              const SizedBox(height: 16),
              _buildSourceOption(
                context,
                title: '手機相簿',
                description: '從相簿選擇圖片',
                icon: Icons.photo,
                onTap: () async {
                  Navigator.of(context).pop();
                  try {
                    final imageSource = await pickFromGallery();
                    if (imageSource != null && context.mounted) {
                      Navigator.of(context).pop(imageSource);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      _showErrorDialog(context, e.toString());
                    }
                  }
                },
              ),
              const SizedBox(height: 16),
              _buildSourceOption(
                context,
                title: '相機拍攝',
                description: '使用相機拍攝新圖片',
                icon: Icons.camera_alt,
                onTap: () async {
                  Navigator.of(context).pop();
                  try {
                    final imageSource = await pickFromCamera();
                    if (imageSource != null && context.mounted) {
                      Navigator.of(context).pop(imageSource);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      _showErrorDialog(context, e.toString());
                    }
                  }
                },
              ),
              const SizedBox(height: 16),
              _buildSourceOption(
                context,
                title: 'AI 產生',
                description: '使用 AI 產生圖片（開發中）',
                icon: Icons.auto_awesome,
                onTap: () {
                  Navigator.of(context).pop();
                  _showComingSoonDialog(context);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(fontSize: 18)),
            ),
          ],
        );
      },
    );
  }

  /// 建立來源選項 Widget
  static Widget _buildSourceOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(icon, size: 48, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 20),
          ],
        ),
      ),
    );
  }

  /// 顯示內建圖片選擇對話框
  static Future<void> _showBuiltinImagesDialog(BuildContext context) async {
    final builtinImages = getBuiltinImages();

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '選擇內建圖片',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: builtinImages.length,
              itemBuilder: (context, index) {
                final imageSource = builtinImages[index];
                return InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(imageSource);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(8),
                              ),
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.image,
                                size: 48,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            imageSource.name,
                            style: const TextStyle(fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(fontSize: 18)),
            ),
          ],
        );
      },
    );
  }

  /// 顯示錯誤對話框
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('錯誤'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  /// 顯示功能開發中對話框
  static void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('功能開發中'),
          content: const Text('AI 圖片產生功能正在開發中，敬請期待！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }
}
