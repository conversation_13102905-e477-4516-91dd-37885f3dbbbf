import 'package:flutter/material.dart';
import '../models/image_source.dart';
import '../services/image_picker_service.dart';

/// 圖片選擇畫面
/// 提供多種圖片來源選擇方式
class ImageSelectionScreen extends StatefulWidget {
  const ImageSelectionScreen({super.key});

  @override
  State<ImageSelectionScreen> createState() => _ImageSelectionScreenState();
}

class _ImageSelectionScreenState extends State<ImageSelectionScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '選擇圖片',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        '請選擇圖片來源',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '選擇您想要的背景圖片來製作長輩圖',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),
                      // 使用固定高度的 GridView
                      SizedBox(
                        height: 400, // 固定高度避免溢出
                        child: GridView.count(
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          children: [
                            _buildImageSourceCard(
                              title: '內建圖庫',
                              description: '精選背景圖片',
                              icon: Icons.photo_library,
                              color: Colors.blue,
                              onTap: () => _selectBuiltinImage(),
                            ),
                            _buildImageSourceCard(
                              title: '手機相簿',
                              description: '選擇相簿圖片',
                              icon: Icons.photo,
                              color: Colors.green,
                              onTap: () => _selectFromGallery(),
                            ),
                            _buildImageSourceCard(
                              title: '相機拍攝',
                              description: '拍攝新圖片',
                              icon: Icons.camera_alt,
                              color: Colors.orange,
                              onTap: () => _selectFromCamera(),
                            ),
                            _buildImageSourceCard(
                              title: 'AI 產生',
                              description: '智能產生圖片',
                              icon: Icons.auto_awesome,
                              color: Colors.purple,
                              onTap: () => _showComingSoon(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        '💡 提示：選擇圖片後，您可以在上面加上祝福語',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 40), // 額外底部間距
                    ],
                  ),
                ),
              ),
    );
  }

  /// 建立圖片來源卡片
  Widget _buildImageSourceCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, size: 48, color: color),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 選擇內建圖片
  Future<void> _selectBuiltinImage() async {
    setState(() => _isLoading = true);

    try {
      final builtinImages = ImagePickerService.getBuiltinImages();

      if (mounted) {
        final selectedImage = await _showBuiltinImageGrid(builtinImages);
        if (selectedImage != null && mounted) {
          Navigator.of(context).pop(selectedImage);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('載入內建圖片時發生錯誤：$e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 從相簿選擇圖片
  Future<void> _selectFromGallery() async {
    setState(() => _isLoading = true);

    try {
      final imageSource = await ImagePickerService.pickFromGallery();
      if (imageSource != null && mounted) {
        Navigator.of(context).pop(imageSource);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 從相機拍攝圖片
  Future<void> _selectFromCamera() async {
    setState(() => _isLoading = true);

    try {
      final imageSource = await ImagePickerService.pickFromCamera();
      if (imageSource != null && mounted) {
        Navigator.of(context).pop(imageSource);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 顯示內建圖片網格
  Future<ImageSource?> _showBuiltinImageGrid(List<ImageSource> images) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '選擇背景圖片',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: images.length,
              itemBuilder: (context, index) {
                final image = images[index];
                return InkWell(
                  onTap: () => Navigator.of(context).pop(image),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(12),
                              ),
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.image,
                                size: 48,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            image.name,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(fontSize: 18)),
            ),
          ],
        );
      },
    );
  }

  /// 顯示功能開發中提示
  void _showComingSoon() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('功能開發中'),
          content: const Text('AI 圖片產生功能正在開發中，敬請期待！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  /// 顯示錯誤訊息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
