import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(const EverydaySafetyApp());
}

/// 長輩圖產生器主應用程式
class EverydaySafetyApp extends StatelessWidget {
  const EverydaySafetyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '長輩圖產生器',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // 使用適合長輩的配色方案
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,

        // 設定字型
        textTheme: GoogleFonts.notoSansTcTextTheme(
          Theme.of(context).textTheme,
        ).apply(bodyColor: Colors.black87, displayColor: Colors.black87),

        // 設定大字體，適合長輩使用
        fontFamily: GoogleFonts.notoSansTc().fontFamily,

        // 按鈕主題
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            textStyle: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),

        // 卡片主題
        cardTheme: CardTheme(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),

        // AppBar 主題
        appBarTheme: AppBarTheme(
          elevation: 0,
          centerTitle: true,
          titleTextStyle: GoogleFonts.notoSansTc(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      home: const HomeScreen(),
    );
  }
}
