/// 祝福語模型
class BlessingText {
  final String id;
  final String text;
  final String category;
  final bool isCustom;

  BlessingText({
    required this.id,
    required this.text,
    required this.category,
    this.isCustom = false,
  });

  /// 預設祝福語列表
  static List<BlessingText> getDefaultBlessings() {
    return [
      // 健康類
      BlessingText(
        id: 'health_1',
        text: '身體健康\n萬事如意',
        category: '健康',
      ),
      BlessingText(
        id: 'health_2',
        text: '健康長壽\n平安喜樂',
        category: '健康',
      ),
      BlessingText(
        id: 'health_3',
        text: '身強體壯\n精神飽滿',
        category: '健康',
      ),

      // 財運類
      BlessingText(
        id: 'wealth_1',
        text: '恭喜發財\n年年有餘',
        category: '財運',
      ),
      BlessingText(
        id: 'wealth_2',
        text: '財源廣進\n生意興隆',
        category: '財運',
      ),
      BlessingText(
        id: 'wealth_3',
        text: '招財進寶\n富貴滿堂',
        category: '財運',
      ),

      // 節慶類
      BlessingText(
        id: 'festival_1',
        text: '新年快樂\n龍年大吉',
        category: '節慶',
      ),
      BlessingText(
        id: 'festival_2',
        text: '中秋節快樂\n月圓人團圓',
        category: '節慶',
      ),
      BlessingText(
        id: 'festival_3',
        text: '端午節安康\n粽子香滿堂',
        category: '節慶',
      ),

      // 家庭類
      BlessingText(
        id: 'family_1',
        text: '家和萬事興\n闔家平安',
        category: '家庭',
      ),
      BlessingText(
        id: 'family_2',
        text: '天倫之樂\n幸福美滿',
        category: '家庭',
      ),
      BlessingText(
        id: 'family_3',
        text: '子孫滿堂\n福壽雙全',
        category: '家庭',
      ),

      // 工作類
      BlessingText(
        id: 'work_1',
        text: '工作順利\n步步高升',
        category: '工作',
      ),
      BlessingText(
        id: 'work_2',
        text: '事業有成\n前程似錦',
        category: '工作',
      ),
      BlessingText(
        id: 'work_3',
        text: '升官發財\n心想事成',
        category: '工作',
      ),

      // 愛情類
      BlessingText(
        id: 'love_1',
        text: '百年好合\n永結同心',
        category: '愛情',
      ),
      BlessingText(
        id: 'love_2',
        text: '鴛鴦戲水\n比翼雙飛',
        category: '愛情',
      ),
      BlessingText(
        id: 'love_3',
        text: '情深似海\n愛意綿綿',
        category: '愛情',
      ),
    ];
  }

  /// 根據分類取得祝福語
  static List<BlessingText> getBlessingsByCategory(String category) {
    return getDefaultBlessings()
        .where((blessing) => blessing.category == category)
        .toList();
  }

  /// 取得所有分類
  static List<String> getAllCategories() {
    final blessings = getDefaultBlessings();
    final categories = blessings.map((b) => b.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// 建立自訂祝福語
  static BlessingText createCustom(String text) {
    return BlessingText(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      text: text,
      category: '自訂',
      isCustom: true,
    );
  }

  @override
  String toString() {
    return 'BlessingText(id: $id, text: $text, category: $category, isCustom: $isCustom)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BlessingText && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
