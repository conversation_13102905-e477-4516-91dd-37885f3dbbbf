import 'package:flutter/material.dart';

/// 圖片來源類型
enum ImageSourceType {
  builtin, // 內建圖庫
  gallery, // 手機相簿
  camera, // 相機拍攝
  ai, // AI 產生
}

/// 圖片來源模型
class ImageSource {
  final String id;
  final String name;
  final String? path;
  final String? url;
  final ImageSourceType type;
  final String? description;
  final String? thumbnail;

  ImageSource({
    required this.id,
    required this.name,
    this.path,
    this.url,
    required this.type,
    this.description,
    this.thumbnail,
  });

  /// 內建圖片列表
  static List<ImageSource> getBuiltinImages() {
    return [
      ImageSource(
        id: 'builtin_1',
        name: '花朵背景',
        path: 'assets/images/flowers.jpg',
        type: ImageSourceType.builtin,
        description: '美麗的花朵背景，適合祝福語',
        thumbnail: 'assets/images/flowers_thumb.jpg',
      ),
      ImageSource(
        id: 'builtin_2',
        name: '風景背景',
        path: 'assets/images/landscape.jpg',
        type: ImageSourceType.builtin,
        description: '寧靜的風景背景',
        thumbnail: 'assets/images/landscape_thumb.jpg',
      ),
      ImageSource(
        id: 'builtin_3',
        name: '動物背景',
        path: 'assets/images/animals.jpg',
        type: ImageSourceType.builtin,
        description: '可愛的動物背景',
        thumbnail: 'assets/images/animals_thumb.jpg',
      ),
      ImageSource(
        id: 'builtin_4',
        name: '節慶背景',
        path: 'assets/images/festival.jpg',
        type: ImageSourceType.builtin,
        description: '節慶慶祝背景',
        thumbnail: 'assets/images/festival_thumb.jpg',
      ),
      ImageSource(
        id: 'builtin_5',
        name: '祝福背景',
        path: 'assets/images/blessing.jpg',
        type: ImageSourceType.builtin,
        description: '溫馨的祝福背景',
        thumbnail: 'assets/images/blessing_thumb.jpg',
      ),
    ];
  }

  /// 建立相簿圖片來源
  static ImageSource fromGallery(String path) {
    return ImageSource(
      id: 'gallery_${DateTime.now().millisecondsSinceEpoch}',
      name: '相簿圖片',
      path: path,
      type: ImageSourceType.gallery,
      description: '從相簿選取的圖片',
    );
  }

  /// 建立相機圖片來源
  static ImageSource fromCamera(String path) {
    return ImageSource(
      id: 'camera_${DateTime.now().millisecondsSinceEpoch}',
      name: '相機拍攝',
      path: path,
      type: ImageSourceType.camera,
      description: '使用相機拍攝的圖片',
    );
  }

  /// 建立 AI 圖片來源
  static ImageSource fromAI(String url, String description) {
    return ImageSource(
      id: 'ai_${DateTime.now().millisecondsSinceEpoch}',
      name: 'AI 產生圖片',
      url: url,
      type: ImageSourceType.ai,
      description: description,
    );
  }

  /// 取得圖片路徑或 URL
  String? get imageSource => path ?? url;

  /// 是否為本地圖片
  bool get isLocal => path != null;

  /// 是否為網路圖片
  bool get isNetwork => url != null;

  @override
  String toString() {
    return 'ImageSource(id: $id, name: $name, type: $type, path: $path, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageSource && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 圖片選擇選項
class ImagePickerOption {
  final String title;
  final String description;
  final IconData icon;
  final ImageSourceType type;
  final VoidCallback onTap;

  ImagePickerOption({
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.onTap,
  });
}
