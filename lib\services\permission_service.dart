import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

/// 權限管理服務
/// 處理 Android/iOS 平台的權限請求
class PermissionService {
  /// 請求相簿存取權限
  /// 
  /// 根據不同 Android 版本處理不同權限：
  /// - Android 13+ (API 33+): READ_MEDIA_IMAGES
  /// - Android 10-12: READ_EXTERNAL_STORAGE
  /// - Android 9-: WRITE_EXTERNAL_STORAGE
  /// 
  /// iOS: 使用 NSPhotoLibraryAddUsageDescription
  static Future<bool> requestPhotoPermission() async {
    if (Platform.isAndroid) {
      return await _requestAndroidPhotoPermission();
    } else if (Platform.isIOS) {
      return await _requestIOSPhotoPermission();
    }
    return false;
  }

  /// 請求相機權限
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// 檢查相簿權限狀態
  static Future<bool> checkPhotoPermission() async {
    if (Platform.isAndroid) {
      return await _checkAndroidPhotoPermission();
    } else if (Platform.isIOS) {
      return await _checkIOSPhotoPermission();
    }
    return false;
  }

  /// Android 相簿權限請求
  static Future<bool> _requestAndroidPhotoPermission() async {
    // Android 13+ (API 33+)
    if (await _isAndroid13OrHigher()) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    
    // Android 10-12 (API 29-32)
    if (await _isAndroid10OrHigher()) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    
    // Android 9- (API 28-)
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  /// iOS 相簿權限請求
  static Future<bool> _requestIOSPhotoPermission() async {
    final status = await Permission.photos.request();
    return status.isGranted;
  }

  /// 檢查 Android 相簿權限
  static Future<bool> _checkAndroidPhotoPermission() async {
    if (await _isAndroid13OrHigher()) {
      return await Permission.photos.isGranted;
    }
    return await Permission.storage.isGranted;
  }

  /// 檢查 iOS 相簿權限
  static Future<bool> _checkIOSPhotoPermission() async {
    return await Permission.photos.isGranted;
  }

  /// 檢查是否為 Android 13+
  static Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    // 這裡可以加入更精確的 API 版本檢查
    return true; // 簡化處理，實際可用 device_info_plus 檢查
  }

  /// 檢查是否為 Android 10+
  static Future<bool> _isAndroid10OrHigher() async {
    if (!Platform.isAndroid) return false;
    return true; // 簡化處理
  }

  /// 開啟應用程式設定頁面
  static Future<void> openAppSettings() async {
    await openAppSettings();
  }

  /// 顯示權限說明對話框
  static String getPermissionRationale() {
    if (Platform.isAndroid) {
      return '此應用程式需要存取您的相簿權限，以便儲存產生的長輩圖。請在設定中允許相簿權限。';
    } else {
      return '此應用程式需要存取您的照片權限，以便儲存產生的長輩圖。請在設定中允許照片權限。';
    }
  }
}
