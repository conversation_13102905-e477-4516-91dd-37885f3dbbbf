import 'package:flutter_test/flutter_test.dart';
import 'package:everyday_safety/models/blessing_text.dart';
import 'package:everyday_safety/models/image_source.dart';

void main() {
  group('BlessingText Tests', () {
    test('should create default blessings', () {
      final blessings = BlessingText.getDefaultBlessings();
      expect(blessings.isNotEmpty, true);
      expect(blessings.length, greaterThan(10));
    });

    test('should get blessings by category', () {
      final healthBlessings = BlessingText.getBlessingsByCategory('健康');
      expect(healthBlessings.isNotEmpty, true);
      expect(healthBlessings.every((b) => b.category == '健康'), true);
    });

    test('should get all categories', () {
      final categories = BlessingText.getAllCategories();
      expect(categories.contains('健康'), true);
      expect(categories.contains('財運'), true);
      expect(categories.contains('節慶'), true);
    });

    test('should create custom blessing', () {
      final custom = BlessingText.createCustom('自訂祝福語');
      expect(custom.text, '自訂祝福語');
      expect(custom.category, '自訂');
      expect(custom.isCustom, true);
    });
  });

  group('ImageSource Tests', () {
    test('should create builtin images', () {
      final builtinImages = ImageSource.getBuiltinImages();
      expect(builtinImages.isNotEmpty, true);
      expect(builtinImages.every((img) => img.type == ImageSourceType.builtin), true);
    });

    test('should create gallery image source', () {
      final galleryImage = ImageSource.fromGallery('/path/to/image.jpg');
      expect(galleryImage.type, ImageSourceType.gallery);
      expect(galleryImage.path, '/path/to/image.jpg');
      expect(galleryImage.isLocal, true);
      expect(galleryImage.isNetwork, false);
    });

    test('should create camera image source', () {
      final cameraImage = ImageSource.fromCamera('/path/to/camera.jpg');
      expect(cameraImage.type, ImageSourceType.camera);
      expect(cameraImage.path, '/path/to/camera.jpg');
      expect(cameraImage.isLocal, true);
    });

    test('should create AI image source', () {
      final aiImage = ImageSource.fromAI('https://example.com/ai.jpg', 'AI generated');
      expect(aiImage.type, ImageSourceType.ai);
      expect(aiImage.url, 'https://example.com/ai.jpg');
      expect(aiImage.isNetwork, true);
      expect(aiImage.isLocal, false);
    });
  });
}
